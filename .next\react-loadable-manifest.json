{"..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch", "files": []}, "..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "app\\ad\\[id]\\page.tsx -> @/components/ui/ConfirmationDialog": {"id": "app\\ad\\[id]\\page.tsx -> @/components/ui/ConfirmationDialog", "files": ["static/chunks/_app-pages-browser_src_components_ui_ConfirmationDialog_tsx.js"]}, "lib\\services\\vendorShops.ts -> ./shopProducts": {"id": "lib\\services\\vendorShops.ts -> ./shopProducts", "files": ["static/chunks/_app-pages-browser_src_lib_services_shopProducts_ts.js"]}}