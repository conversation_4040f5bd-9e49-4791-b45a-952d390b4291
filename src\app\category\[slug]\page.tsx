'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Filter, MapPin, Calendar, Eye, Heart, Share2, Grid, List, ChevronDown, Sparkles, TrendingUp, SlidersHorizontal } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import AdGrid from '@/components/ads/AdGrid'
import CategoryPageFilters from '@/components/filters/CategoryPageFilters'
import { PremiumButton, PremiumCard, PremiumInput, FadeIn, SlideInUp, StaggerContainer, StaggerItem } from '@/components/ui/premium'
import { CategoryService } from '@/lib/services/categories'
import { AdService } from '@/lib/services/ads'
import { Category, AdWithDetails, SearchFilters } from '@/types'
import { formatCurrency } from '@/lib/utils'

export default function CategoryPage() {
  const params = useParams()
  const slug = params.slug as string
  
  const [category, setCategory] = useState<Category | null>(null)
  const [ads, setAds] = useState<AdWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filters, setFilters] = useState<SearchFilters>({
    category_id: '',
    subcategory_id: '',
    location: '',
    min_price: '',
    max_price: '',
    condition: '',
    sortBy: 'newest'
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    if (slug) {
      loadCategoryData()
    }
  }, [slug])

  useEffect(() => {
    if (category) {
      loadAds()
    }
  }, [category, filters, pagination.page, sortBy])

  const loadCategoryData = async () => {
    try {
      const categoryData = await CategoryService.getCategoryBySlug(slug)
      if (categoryData) {
        setCategory(categoryData)
        setFilters(prev => ({ ...prev, category_id: categoryData.id }))
      }
    } catch (error) {
      console.error('Error loading category:', error)
    }
  }

  const loadAds = async () => {
    if (!category) return

    try {
      setLoading(true)
      const searchFilters = {
        ...filters,
        category_id: category.id,
        sortBy: sortBy
      }

      let result
      if (searchQuery.trim()) {
        result = await AdService.searchAds(searchQuery, searchFilters, pagination.page, pagination.limit)
      } else {
        result = await AdService.getAds(searchFilters, pagination.page, pagination.limit)
      }

      setAds(result.ads)
      setPagination(prev => ({
        ...prev,
        total: result.meta.total,
        totalPages: result.meta.totalPages
      }))
    } catch (error) {
      console.error('Error loading ads:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 }))
    loadAds()
  }

  const handleSortChange = (newSort: string) => {
    setSortBy(newSort)
    setFilters(prev => ({ ...prev, sortBy: newSort }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  if (!category && !loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Category not found</h1>
            <p className="text-gray-600">The category you're looking for doesn't exist.</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="py-6 pt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Enhanced Breadcrumb */}
          <FadeIn>
            <nav className="flex mb-8" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2 text-sm">
                <li>
                  <motion.a
                    href="/"
                    className="text-gray-500 hover:text-primary-blue font-medium transition-colors"
                    whileHover={{ scale: 1.05 }}
                  >
                    Home
                  </motion.a>
                </li>
                <li>
                  <span className="text-gray-400 mx-2">›</span>
                  <motion.a
                    href="/ads"
                    className="text-gray-500 hover:text-primary-blue font-medium transition-colors"
                    whileHover={{ scale: 1.05 }}
                  >
                    All ads
                  </motion.a>
                </li>
                <li>
                  <span className="text-gray-400 mx-2">›</span>
                  <span className="text-gray-900 font-bold">{category?.name}</span>
                </li>
              </ol>
            </nav>
          </FadeIn>

          {/* Enhanced Header Section */}
          <SlideInUp>
            <PremiumCard variant="premium" className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <motion.div
                    className="p-3 bg-gradient-to-br from-primary-blue to-secondary-blue text-white"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <TrendingUp className="h-6 w-6" />
                  </motion.div>
                  <div>
                    <h1 className="text-3xl font-bold font-heading text-gray-900">
                      {category?.name}
                    </h1>
                    <p className="text-gray-600 font-medium">Browse and discover amazing deals</p>
                  </div>
                </div>

                <motion.button
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Heart className="h-5 w-5" />
                  <span className="text-sm font-medium">Save search</span>
                </motion.button>
              </div>

              <div className="mb-6">
                <motion.p
                  className="text-gray-600 flex items-center gap-2"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Sparkles className="h-4 w-4 text-primary-blue" />
                  Showing <span className="font-bold text-primary-blue">1-{Math.min(pagination.limit, pagination.total)}</span> of <span className="font-bold text-primary-blue">{pagination.total}</span> ads
                </motion.p>
              </div>

              {/* Enhanced Search Bar */}
              <motion.form
                onSubmit={handleSearch}
                className="relative"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <motion.input
                    type="text"
                    placeholder="What are you looking for in this category?"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-32 py-4 border-2 border-gray-200 hover:border-primary-blue/30 focus:border-primary-blue focus:ring-4 focus:ring-primary-blue/10 transition-all duration-300 text-lg"
                    whileFocus={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  />
                  <motion.button
                    type="submit"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-primary-blue to-secondary-blue hover:from-primary-600 hover:to-secondary-600 text-white px-6 py-3 font-bold transition-all duration-300 shadow-lg"
                    whileHover={{ scale: 1.05, boxShadow: '0 10px 25px rgba(0, 85, 159, 0.3)' }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Search className="h-5 w-5" />
                  </motion.button>
                </div>
              </motion.form>
            </PremiumCard>
          </SlideInUp>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Enhanced Left Sidebar - Filters */}
            <AnimatePresence>
              <motion.div
                className={`w-full lg:w-80 flex-shrink-0 ${showMobileFilters ? 'block' : 'hidden lg:block'}`}
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.3 }}
              >
                <div className="sticky top-32">
                  {category && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <CategoryPageFilters
                        category={category}
                        filters={filters}
                        onFilterChange={handleFilterChange}
                      />
                    </motion.div>
                  )}
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Enhanced Main Content Area */}
            <div className="flex-1 min-w-0">
              {/* Enhanced Mobile Filter Toggle */}
              <div className="lg:hidden mb-6">
                <PremiumButton
                  variant="outline"
                  onClick={() => setShowMobileFilters(!showMobileFilters)}
                  icon={<SlidersHorizontal className="h-4 w-4" />}
                  className="w-full justify-center"
                >
                  {showMobileFilters ? 'Hide Filters' : 'Show Filters'}
                </PremiumButton>
              </div>

              {/* Enhanced Results Header with Sort */}
              <SlideInUp delay={0.4}>
                <PremiumCard variant="premium" className="mb-6">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div>
                      <motion.h2
                        className="text-xl font-bold font-heading text-gray-900 mb-1"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.5 }}
                      >
                        Search Results
                      </motion.h2>
                      <motion.p
                        className="text-gray-600 flex items-center gap-2"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.6 }}
                      >
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        Showing <span className="font-bold text-primary-blue">{((pagination.page - 1) * pagination.limit) + 1}-{Math.min(pagination.page * pagination.limit, pagination.total)}</span> of <span className="font-bold text-primary-blue">{pagination.total}</span> results
                      </motion.p>
                    </div>

                    <div className="flex items-center gap-4">
                      {/* Enhanced View Mode Toggle */}
                      <motion.div
                        className="flex items-center bg-gray-100 p-1 shadow-inner"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.7 }}
                      >
                        <motion.button
                          onClick={() => setViewMode('grid')}
                          className={`p-3 transition-all duration-300 ${
                            viewMode === 'grid'
                              ? 'bg-primary-blue text-white shadow-lg'
                              : 'text-gray-500 hover:text-gray-700 hover:bg-white'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Grid className="h-4 w-4" />
                        </motion.button>
                        <motion.button
                          onClick={() => setViewMode('list')}
                          className={`p-3 transition-all duration-300 ${
                            viewMode === 'list'
                              ? 'bg-primary-blue text-white shadow-lg'
                              : 'text-gray-500 hover:text-gray-700 hover:bg-white'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <List className="h-4 w-4" />
                        </motion.button>
                      </motion.div>

                      {/* Enhanced Sort Dropdown */}
                      <motion.div
                        className="relative"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.8 }}
                      >
                        <select
                          value={sortBy}
                          onChange={(e) => handleSortChange(e.target.value)}
                          className="appearance-none bg-white border-2 border-gray-200 hover:border-primary-blue/30 focus:border-primary-blue focus:ring-4 focus:ring-primary-blue/10 px-4 py-3 pr-10 text-sm font-medium text-gray-700 transition-all duration-300 cursor-pointer"
                        >
                          <option value="newest">Newest First</option>
                          <option value="oldest">Oldest First</option>
                          <option value="price_low">Price: Low to High</option>
                          <option value="price_high">Price: High to Low</option>
                          <option value="most_viewed">Most Viewed</option>
                        </select>
                        <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
                      </motion.div>
                    </div>
                  </div>
                </PremiumCard>
              </SlideInUp>

              {/* Enhanced Ad Grid */}
              <AnimatePresence mode="wait">
                {loading ? (
                  <motion.div
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    {[...Array(6)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: i * 0.1 }}
                      >
                        <SkeletonCard variant="product" animated />
                      </motion.div>
                    ))}
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <AdGrid
                      ads={ads}
                      loading={loading}
                      layout="category"
                      emptyMessage={
                        <div className="text-center py-16">
                          <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                          >
                            <div className="text-6xl mb-4">🔍</div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">No ads found in {category?.name}</h3>
                            <p className="text-gray-600 mb-6">Be the first to post an ad in this category!</p>
                            <PremiumButton
                              variant="primary"
                              onClick={() => window.location.href = '/post-ad'}
                              icon={<Sparkles className="h-4 w-4" />}
                            >
                              Post First Ad
                            </PremiumButton>
                          </motion.div>
                        </div>
                      }
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
