"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_services_shopProducts_ts"],{

/***/ "(app-pages-browser)/./src/lib/services/shopProducts.ts":
/*!******************************************!*\
  !*** ./src/lib/services/shopProducts.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShopProductService: function() { return /* binding */ ShopProductService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n/**\n * ShopProductService - Service for managing independent shop products\n * Products are completely separate from the ads system\n */ class ShopProductService {\n    /**\n   * Create a new shop product\n   */ static async createProduct(productData, imageUrls) {\n        try {\n            // Create the product\n            const { data: product, error: productError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).insert({\n                ...productData,\n                currency: productData.currency || \"LKR\",\n                condition: productData.condition || \"new\",\n                min_order_quantity: productData.min_order_quantity || 1,\n                is_digital: productData.is_digital || false,\n                negotiable: productData.negotiable || false,\n                status: productData.status || \"active\"\n            }).select(\"\\n          *,\\n          shop:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS, \"(*),\\n          category:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES, \"(*),\\n          subcategory:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES, \"(*)\\n        \")).single();\n            if (productError) {\n                console.error(\"Error creating product:\", productError);\n                throw new Error(\"Failed to create product: \".concat(productError.message));\n            }\n            // Add images if provided\n            if (imageUrls && imageUrls.length > 0) {\n                const imageInserts = imageUrls.map((url, index)=>({\n                        product_id: product.id,\n                        image_url: url,\n                        sort_order: index,\n                        is_primary: index === 0\n                    }));\n                const { error: imageError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES).insert(imageInserts);\n                if (imageError) {\n                    console.error(\"Error adding product images:\", imageError);\n                // Don't throw here, product is created successfully\n                }\n            }\n            return product;\n        } catch (error) {\n            console.error(\"Error in createProduct:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get products for a shop\n   */ static async getShopProducts(shopId) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20, filters = arguments.length > 3 ? arguments[3] : void 0;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"\\n          *,\\n          shop:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS, \"(*),\\n          category:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES, \"(*),\\n          subcategory:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES, \"(*),\\n          images:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES, \"(*)\\n        \"), {\n                count: \"exact\"\n            }).eq(\"shop_id\", shopId);\n            // Apply filters\n            if (filters === null || filters === void 0 ? void 0 : filters.category_id) {\n                query = query.eq(\"category_id\", filters.category_id);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.subcategory_id) {\n                query = query.eq(\"subcategory_id\", filters.subcategory_id);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.search) {\n                query = query.or(\"title.ilike.%\".concat(filters.search, \"%,description.ilike.%\").concat(filters.search, \"%\"));\n            }\n            const { data, error, count } = await query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching shop products:\", error);\n                throw new Error(\"Failed to fetch products: \".concat(error.message));\n            }\n            // Return products without refreshing statistics to improve performance\n            // Statistics will be refreshed only when needed (e.g., when viewing individual products)\n            return {\n                products: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error in getShopProducts:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all products across all shops with filters\n   */ static async getAllProducts() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20, filters = arguments.length > 2 ? arguments[2] : void 0;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"\\n          *,\\n          shop:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS, \"(*),\\n          category:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES, \"(*),\\n          subcategory:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES, \"(*),\\n          images:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES, \"(*)\\n        \"), {\n                count: \"exact\"\n            });\n            // Only show products from approved shops with active status\n            query = query.eq(\"shop.status\", \"approved\").eq(\"status\", \"active\");\n            // Apply filters\n            if (filters === null || filters === void 0 ? void 0 : filters.category_id) {\n                query = query.eq(\"category_id\", filters.category_id);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.subcategory_id) {\n                query = query.eq(\"subcategory_id\", filters.subcategory_id);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.shop_id) {\n                query = query.eq(\"shop_id\", filters.shop_id);\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.search) {\n                query = query.or(\"title.ilike.%\".concat(filters.search, \"%,description.ilike.%\").concat(filters.search, \"%\"));\n            }\n            if (filters === null || filters === void 0 ? void 0 : filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            const { data, error, count } = await query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching all products:\", error);\n                throw new Error(\"Failed to fetch products: \".concat(error.message));\n            }\n            // Return products without refreshing statistics to improve performance\n            return {\n                products: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error in getAllProducts:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update a product\n   */ static async updateProduct(id, productData) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).update(productData).eq(\"id\", id).select(\"\\n          *,\\n          shop:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS, \"(*),\\n          category:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES, \"(*),\\n          subcategory:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES, \"(*),\\n          images:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES, \"(*)\\n        \")).single();\n            if (error) {\n                console.error(\"Error updating product:\", error);\n                throw new Error(\"Failed to update product: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error in updateProduct:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a product\n   */ static async deleteProduct(id) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).delete().eq(\"id\", id);\n            if (error) {\n                console.error(\"Error deleting product:\", error);\n                throw new Error(\"Failed to delete product: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error in deleteProduct:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Refresh product review statistics\n   */ static async refreshProductReviewStats(productId) {\n        try {\n            // Get review statistics\n            const { data: reviewStats } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.PRODUCT_REVIEWS).select(\"rating\").eq(\"product_id\", productId);\n            const reviews = reviewStats || [];\n            const totalReviews = reviews.length;\n            const averageRating = totalReviews > 0 ? reviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n            // Update product with calculated statistics\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).update({\n                total_reviews: totalReviews,\n                average_rating: Math.round(averageRating * 100) / 100\n            }).eq(\"id\", productId);\n        } catch (error) {\n            console.error(\"Error refreshing product review stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Increment product view count\n   */ static async incrementProductViews(productId) {\n        try {\n            // First get current views count\n            const { data: currentProduct } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"views\").eq(\"id\", productId).single();\n            const currentViews = (currentProduct === null || currentProduct === void 0 ? void 0 : currentProduct.views) || 0;\n            // Update with incremented views\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).update({\n                views: currentViews + 1\n            }).eq(\"id\", productId);\n            if (error) {\n                console.error(\"Error incrementing product views:\", error);\n            // Don't throw error for view counting failures\n            }\n        } catch (error) {\n            console.error(\"Error in incrementProductViews:\", error);\n        // Don't throw error for view counting failures\n        }\n    }\n    /**\n   * Get product by ID with review statistics\n   */ static async getProductById(id) {\n        try {\n            // First refresh the review statistics to ensure they're current\n            await this.refreshProductReviewStats(id);\n            // Increment view count\n            await this.incrementProductViews(id);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"\\n          *,\\n          shop:\".concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS, \"(*),\\n          category:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES, \"(*),\\n          subcategory:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES, \"(*),\\n          images:\").concat(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES, \"(*)\\n        \")).eq(\"id\", id).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Not found\n                    ;\n                }\n                throw new Error(\"Failed to fetch product: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error in getProductById:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Add images to a product\n   */ static async addProductImages(productId, imageUrls) {\n        try {\n            const imageInserts = imageUrls.map((url, index)=>({\n                    product_id: productId,\n                    image_url: url,\n                    sort_order: index,\n                    is_primary: index === 0\n                }));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES).insert(imageInserts).select();\n            if (error) {\n                console.error(\"Error adding product images:\", error);\n                throw new Error(\"Failed to add images: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error in addProductImages:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Remove product image\n   */ static async removeProductImage(imageId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES).delete().eq(\"id\", imageId);\n            if (error) {\n                console.error(\"Error removing product image:\", error);\n                throw new Error(\"Failed to remove image: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error in removeProductImage:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/shopProducts.ts\n"));

/***/ })

}]);